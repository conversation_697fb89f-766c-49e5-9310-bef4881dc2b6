using AutoMapper;
using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.RouteCategory.Commands.CreateRouteCategory
{
    public class CreateRouteCategoryCommandHandler : IRequestHandler<CreateRouteCategoryCommand, ActionResponse<bool>>
    {
        private readonly IRouteCategoryRepository _routeCategoryRepository;
        private readonly IMapper _mapper;
        private readonly IAppLogger<CreateRouteCategoryCommandHandler> _logger;
        private readonly IUnitOfWork _uow;
        private readonly IUserPrincipal _userPrincipal;

        public CreateRouteCategoryCommandHandler(IRouteCategoryRepository routeCategoryRepository,
                                               IMapper mapper,
                                               IAppLogger<CreateRouteCategoryCommandHandler> logger,
                                               IUnitOfWork uow,
                                               IUserPrincipal userPrincipal)
        {
            _routeCategoryRepository = routeCategoryRepository;
            _mapper = mapper;
            _logger = logger;
            _uow = uow;
            _userPrincipal = userPrincipal;
        }

        public async Task<ActionResponse<bool>> Handle(CreateRouteCategoryCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var validator = new CreateRouteCategoryCommandValidator(_routeCategoryRepository);
                var validatorResult = await validator.ValidateAsync(request, cancellationToken);
                
                if (validatorResult.IsValid)
                {
                    var routeCategories = new List<Domain.Entities.RouteCategory>();
                    
                    foreach (var routeCategoryRequest in request.RouteCategories)
                    {
                        var routeCategory = _mapper.Map<Domain.Entities.RouteCategory>(routeCategoryRequest);
                        routeCategory.Id = Guid.NewGuid();
                        routeCategory.UserId = _userPrincipal.UserId;
                        routeCategory.ProjectId = _userPrincipal.ProjectId.ToGuid() ?? throw new NotFoundException("ProjectId", _userPrincipal.ProjectId);
                        routeCategory.LanguageId = routeCategoryRequest.LanguageId;
                        routeCategory.GroupId = routeCategoryRequest.GroupId ?? Guid.NewGuid();
                        routeCategory.DateCreated = DateTime.Now;
                        
                        routeCategories.Add(routeCategory);
                    }

                    await _routeCategoryRepository.CreateRangeAsync(routeCategories);
                    await _uow.SaveChangesAsync(cancellationToken);
                    
                    _logger.LogInformation("Route categories created successfully", request.RouteCategories.Count);
                    return ActionResponse<bool>.Success(true, StatusCode.Ok);
                }
                
                return ActionResponse<bool>.Fail(validatorResult.Errors.First().ErrorMessage, StatusCode.BadRequest);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Route categories not created", request, ex);
                return ActionResponse<bool>.Fail($"An error occurred. Error Message: {ex.Message}", StatusCode.BadRequest);
            }
        }
    }
}
