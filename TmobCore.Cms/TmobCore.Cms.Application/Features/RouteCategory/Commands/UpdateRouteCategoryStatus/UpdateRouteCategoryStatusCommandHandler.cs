using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.RouteCategory.Commands.UpdateRouteCategoryStatus
{
    public class UpdateRouteCategoryStatusCommandHandler : IRequestHandler<UpdateRouteCategoryStatusCommand, ActionResponse<bool>>
    {
        private readonly IRouteCategoryRepository _routeCategoryRepository;
        private readonly IAppLogger<UpdateRouteCategoryStatusCommandHandler> _logger;
        private readonly IUnitOfWork _uow;

        public UpdateRouteCategoryStatusCommandHandler(IRouteCategoryRepository routeCategoryRepository,
                                                     IAppLogger<UpdateRouteCategoryStatusCommandHandler> logger,
                                                     IUnitOfWork uow)
        {
            _routeCategoryRepository = routeCategoryRepository;
            _logger = logger;
            _uow = uow;
        }

        public async Task<ActionResponse<bool>> Handle(UpdateRouteCategoryStatusCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var validator = new UpdateRouteCategoryStatusCommandValidator();
                var validatorResult = await validator.ValidateAsync(request, cancellationToken);
                
                if (validatorResult.IsValid)
                {
                    var updatedCount = 0;
                    
                    foreach (var routeCategoryId in request.RouteCategoryIds)
                    {
                        var routeCategory = await _routeCategoryRepository.GetByIdAsync(routeCategoryId);
                        if (routeCategory != null)
                        {
                            routeCategory.Status = (Domain.Common.BaseStatus)request.Status;
                            routeCategory.DateModified = DateTime.Now;
                            _routeCategoryRepository.Update(routeCategory);
                            updatedCount++;
                        }
                    }

                    if (updatedCount == 0)
                    {
                        _logger.LogWarning("No route categories found to update status", request.RouteCategoryIds);
                        return ActionResponse<bool>.Fail("No route categories found to update", StatusCode.NotFound);
                    }

                    await _uow.SaveChangesAsync(cancellationToken);
                    
                    _logger.LogInformation($"{updatedCount} route categories status updated successfully", request.RouteCategoryIds);
                    return ActionResponse<bool>.Success(true, StatusCode.Ok);
                }
                
                return ActionResponse<bool>.Fail(validatorResult.Errors.First().ErrorMessage, StatusCode.BadRequest);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Route categories status not updated", request, ex);
                return ActionResponse<bool>.Fail($"An error occurred. Error Message: {ex.Message}", StatusCode.BadRequest);
            }
        }
    }
}
