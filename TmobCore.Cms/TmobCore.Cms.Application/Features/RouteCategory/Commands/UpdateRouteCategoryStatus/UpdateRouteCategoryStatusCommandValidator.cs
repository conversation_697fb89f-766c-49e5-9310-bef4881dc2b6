using FluentValidation;

namespace TmobCore.Cms.Application.Features.RouteCategory.Commands.UpdateRouteCategoryStatus
{
    public class UpdateRouteCategoryStatusCommandValidator : AbstractValidator<UpdateRouteCategoryStatusCommand>
    {
        public UpdateRouteCategoryStatusCommandValidator()
        {
            RuleFor(x => x.RouteCategoryIds)
                .NotEmpty().WithMessage("At least one route category ID is required")
                .Must(ids => ids.All(id => id != Guid.Empty))
                .WithMessage("All route category IDs must be valid");

            RuleFor(x => x.Status)
                .IsInEnum().WithMessage("Status must be a valid value (0: Draft, 1: Published, 2: Unpublished)");
        }
    }
}
