using FluentValidation;

namespace TmobCore.Cms.Application.Features.RouteCategory.Commands.DeleteRouteCategory
{
    public class DeleteRouteCategoryCommandValidator : AbstractValidator<DeleteRouteCategoryCommand>
    {
        public DeleteRouteCategoryCommandValidator()
        {
            RuleFor(x => x.Ids)
                .NotEmpty().WithMessage("At least one route category ID is required")
                .Must(ids => ids.All(id => id != Guid.Empty))
                .WithMessage("All route category IDs must be valid");
        }
    }
}
