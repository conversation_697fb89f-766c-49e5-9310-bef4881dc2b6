using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.RouteCategory.Commands.DeleteRouteCategory
{
    public class DeleteRouteCategoryCommandHandler : IRequestHandler<DeleteRouteCategoryCommand, ActionResponse<int>>
    {
        private readonly IRouteCategoryRepository _routeCategoryRepository;
        private readonly IAppLogger<DeleteRouteCategoryCommandHandler> _logger;
        private readonly IUnitOfWork _uow;

        public DeleteRouteCategoryCommandHandler(IRouteCategoryRepository routeCategoryRepository,
                                               IAppLogger<DeleteRouteCategoryCommandHandler> logger,
                                               IUnitOfWork uow)
        {
            _routeCategoryRepository = routeCategoryRepository;
            _logger = logger;
            _uow = uow;
        }

        public async Task<ActionResponse<int>> Handle(DeleteRouteCategoryCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var validator = new DeleteRouteCategoryCommandValidator();
                var validatorResult = await validator.ValidateAsync(request, cancellationToken);
                
                if (validatorResult.IsValid)
                {
                    var deletedCount = 0;
                    
                    foreach (var id in request.Ids)
                    {
                        var routeCategory = await _routeCategoryRepository.GetByIdAsync(id);
                        if (routeCategory != null)
                        {
                            routeCategory.Deleted = true;
                            routeCategory.DateModified = DateTime.Now;
                            _routeCategoryRepository.Update(routeCategory);
                            deletedCount++;
                        }
                    }

                    if (deletedCount == 0)
                    {
                        _logger.LogWarning("No route categories found to delete", request.Ids);
                        return ActionResponse<int>.Fail("No route categories found to delete", StatusCode.NotFound);
                    }

                    await _uow.SaveChangesAsync(cancellationToken);
                    
                    _logger.LogInformation($"{deletedCount} route categories deleted successfully", request.Ids);
                    return ActionResponse<int>.Success(deletedCount, StatusCode.Ok);
                }
                
                return ActionResponse<int>.Fail(validatorResult.Errors.First().ErrorMessage, StatusCode.BadRequest);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Route categories not deleted", request, ex);
                return ActionResponse<int>.Fail($"An error occurred. Error Message: {ex.Message}", StatusCode.BadRequest);
            }
        }
    }
}
