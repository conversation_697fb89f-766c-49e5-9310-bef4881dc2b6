using FluentValidation;
using TmobCore.Cms.Application.Contracts.Persistence;

namespace TmobCore.Cms.Application.Features.RouteCategory.Commands.UpdateRouteCategory
{
    public class UpdateRouteCategoryCommandValidator : AbstractValidator<UpdateRouteCategoryCommand>
    {
        private readonly IRouteCategoryRepository _routeCategoryRepository;

        public UpdateRouteCategoryCommandValidator(IRouteCategoryRepository routeCategoryRepository)
        {
            _routeCategoryRepository = routeCategoryRepository;

            RuleFor(x => x.RouteCategories)
                .NotEmpty().WithMessage("At least one route category is required");

            RuleForEach(x => x.RouteCategories).ChildRules(category =>
            {
                category.RuleFor(x => x.Id)
                    .NotEmpty().WithMessage("Route category ID is required");

                category.RuleFor(x => x.Title)
                    .NotEmpty().WithMessage("Title is required")
                    .MaximumLength(255).WithMessage("Title must not exceed 255 characters");

                category.RuleFor(x => x.Description)
                    .MaximumLength(500).WithMessage("Description must not exceed 500 characters");

                category.RuleFor(x => x.Icon)
                    .MaximumLength(255).WithMessage("Icon must not exceed 255 characters");

                category.RuleFor(x => x.LanguageId)
                    .NotEmpty().WithMessage("Language ID is required");

                category.RuleFor(x => x.GroupId)
                    .NotEmpty().WithMessage("Group ID is required");
            });
        }
    }
}
