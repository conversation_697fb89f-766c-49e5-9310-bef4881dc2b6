using AutoMapper;
using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.RouteCategory.Commands.UpdateRouteCategory
{
    public class UpdateRouteCategoryCommandHandler : IRequestHandler<UpdateRouteCategoryCommand, ActionResponse<bool>>
    {
        private readonly IRouteCategoryRepository _routeCategoryRepository;
        private readonly IMapper _mapper;
        private readonly IAppLogger<UpdateRouteCategoryCommandHandler> _logger;
        private readonly IUnitOfWork _uow;

        public UpdateRouteCategoryCommandHandler(IRouteCategoryRepository routeCategoryRepository,
                                               IMapper mapper,
                                               IAppLogger<UpdateRouteCategoryCommandHandler> logger,
                                               IUnitOfWork uow)
        {
            _routeCategoryRepository = routeCategoryRepository;
            _mapper = mapper;
            _logger = logger;
            _uow = uow;
        }

        public async Task<ActionResponse<bool>> Handle(UpdateRouteCategoryCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var validator = new UpdateRouteCategoryCommandValidator(_routeCategoryRepository);
                var validatorResult = await validator.ValidateAsync(request, cancellationToken);
                
                if (validatorResult.IsValid)
                {
                    var updatedCount = 0;
                    
                    foreach (var routeCategoryRequest in request.RouteCategories)
                    {
                        var existingRouteCategory = await _routeCategoryRepository.GetByIdAsync(routeCategoryRequest.Id);
                        if (existingRouteCategory != null)
                        {
                            _mapper.Map(routeCategoryRequest, existingRouteCategory);
                            existingRouteCategory.DateModified = DateTime.Now;
                            
                            _routeCategoryRepository.Update(existingRouteCategory);
                            updatedCount++;
                        }
                    }

                    if (updatedCount == 0)
                    {
                        _logger.LogWarning("No route categories found to update", request.RouteCategories.Select(x => x.Id));
                        return ActionResponse<bool>.Fail("No route categories found to update", StatusCode.NotFound);
                    }

                    await _uow.SaveChangesAsync(cancellationToken);
                    
                    _logger.LogInformation($"{updatedCount} route categories updated successfully", request.RouteCategories.Select(x => x.Id));
                    return ActionResponse<bool>.Success(true, StatusCode.Ok);
                }
                
                return ActionResponse<bool>.Fail(validatorResult.Errors.First().ErrorMessage, StatusCode.BadRequest);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Route categories not updated", request, ex);
                return ActionResponse<bool>.Fail($"An error occurred. Error Message: {ex.Message}", StatusCode.BadRequest);
            }
        }
    }
}
