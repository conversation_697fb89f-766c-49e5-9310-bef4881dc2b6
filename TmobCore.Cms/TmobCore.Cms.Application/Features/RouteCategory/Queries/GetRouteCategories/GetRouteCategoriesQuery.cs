using MediatR;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.RouteCategory;

namespace TmobCore.Cms.Application.Features.RouteCategory.Queries.GetRouteCategories
{
    public class GetRouteCategoriesQuery : IRequest<ActionResponse<List<RouteCategoryResponse>>>
    {
        public string? SearchTerm { get; set; }
        public Guid? GroupId { get; set; }
        public Guid? LanguageId { get; set; }
        public BaseStatus? Status { get; set; }
    }
}
