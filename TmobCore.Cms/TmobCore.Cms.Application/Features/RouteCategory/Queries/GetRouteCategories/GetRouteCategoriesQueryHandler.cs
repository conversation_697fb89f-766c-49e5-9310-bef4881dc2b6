using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.RouteCategory;

namespace TmobCore.Cms.Application.Features.RouteCategory.Queries.GetRouteCategories
{
    public class GetRouteCategoriesQueryHandler : IRequestHandler<GetRouteCategoriesQuery, ActionResponse<List<RouteCategoryResponse>>>
    {
        private readonly IRouteCategoryRepository _routeCategoryRepository;
        private readonly IMapper _mapper;
        private readonly IAppLogger<GetRouteCategoriesQueryHandler> _logger;
        private readonly IUserPrincipal _userPrincipal;

        public GetRouteCategoriesQueryHandler(IRouteCategoryRepository routeCategoryRepository,
                                            IMapper mapper,
                                            IAppLogger<GetRouteCategoriesQueryHandler> logger,
                                            IUserPrincipal userPrincipal)
        {
            _routeCategoryRepository = routeCategoryRepository;
            _mapper = mapper;
            _logger = logger;
            _userPrincipal = userPrincipal;
        }

        public async Task<ActionResponse<List<RouteCategoryResponse>>> Handle(GetRouteCategoriesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var projectId = _userPrincipal.ProjectId.ToGuid();
                
                var query = _routeCategoryRepository.GetQuery(
                    x => !x.Deleted && (projectId == null || x.ProjectId == projectId));

                // Apply filters
                if (!string.IsNullOrEmpty(request.SearchTerm))
                {
                    query = query.Where(x => x.Title.Contains(request.SearchTerm) || 
                                           x.Description.Contains(request.SearchTerm));
                }

                if (request.GroupId.HasValue)
                {
                    query = query.Where(x => x.GroupId == request.GroupId);
                }

                if (request.LanguageId.HasValue)
                {
                    query = query.Where(x => x.LanguageId == request.LanguageId);
                }

                if (request.Status.HasValue)
                {
                    query = query.Where(x => x.Status == (Domain.Common.BaseStatus)request.Status);
                }

                var routeCategories = await query
                    .OrderBy(x => x.DateCreated)
                    .ToListAsync(cancellationToken);

                var response = _mapper.Map<List<RouteCategoryResponse>>(routeCategories);
                
                _logger.LogInformation("Route categories retrieved successfully", routeCategories.Count);
                return ActionResponse<List<RouteCategoryResponse>>.Success(response, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Error retrieving route categories", request, ex);
                return ActionResponse<List<RouteCategoryResponse>>.Fail($"An error occurred. Error Message: {ex.Message}", StatusCode.BadRequest);
            }
        }
    }
}
