using AutoMapper;
using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.RouteCategory;

namespace TmobCore.Cms.Application.Features.RouteCategory.Queries.GetRouteCategoryById
{
    public class GetRouteCategoryByIdQueryHandler : IRequestHandler<GetRouteCategoryByIdQuery, ActionResponse<RouteCategoryResponse>>
    {
        private readonly IRouteCategoryRepository _routeCategoryRepository;
        private readonly IMapper _mapper;
        private readonly IAppLogger<GetRouteCategoryByIdQueryHandler> _logger;

        public GetRouteCategoryByIdQueryHandler(IRouteCategoryRepository routeCategoryRepository,
                                              IMapper mapper,
                                              IAppLogger<GetRouteCategoryByIdQueryHandler> logger)
        {
            _routeCategoryRepository = routeCategoryRepository;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<ActionResponse<RouteCategoryResponse>> Handle(GetRouteCategoryByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var validator = new GetRouteCategoryByIdQueryValidator();
                var validatorResult = await validator.ValidateAsync(request, cancellationToken);
                
                if (validatorResult.IsValid)
                {
                    var routeCategory = await _routeCategoryRepository.GetByIdAsync(request.Id);
                    
                    if (routeCategory == null || routeCategory.Deleted)
                    {
                        _logger.LogWarning("Route category not found", request.Id);
                        return ActionResponse<RouteCategoryResponse>.Fail("Route category not found", StatusCode.NotFound);
                    }

                    var response = _mapper.Map<RouteCategoryResponse>(routeCategory);
                    
                    _logger.LogInformation("Route category retrieved successfully", request.Id);
                    return ActionResponse<RouteCategoryResponse>.Success(response, StatusCode.Ok);
                }
                
                return ActionResponse<RouteCategoryResponse>.Fail(validatorResult.Errors.First().ErrorMessage, StatusCode.BadRequest);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Error retrieving route category", request.Id, ex);
                return ActionResponse<RouteCategoryResponse>.Fail($"An error occurred. Error Message: {ex.Message}", StatusCode.BadRequest);
            }
        }
    }
}
