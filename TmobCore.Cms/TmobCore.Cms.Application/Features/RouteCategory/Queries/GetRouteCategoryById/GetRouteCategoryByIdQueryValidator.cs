using FluentValidation;

namespace TmobCore.Cms.Application.Features.RouteCategory.Queries.GetRouteCategoryById
{
    public class GetRouteCategoryByIdQueryValidator : AbstractValidator<GetRouteCategoryByIdQuery>
    {
        public GetRouteCategoryByIdQueryValidator()
        {
            RuleFor(x => x.Id)
                .NotEmpty().WithMessage("Route category ID is required")
                .NotEqual(Guid.Empty).WithMessage("Route category ID must be a valid GUID");
        }
    }
}
