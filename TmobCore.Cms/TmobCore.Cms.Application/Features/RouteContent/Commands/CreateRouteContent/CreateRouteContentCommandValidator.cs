using FluentValidation;
using TmobCore.Cms.Application.Contracts.Persistence;

namespace TmobCore.Cms.Application.Features.RouteContent.Commands.CreateRouteContent
{
    public class CreateRouteContentCommandValidator : AbstractValidator<CreateRouteContentCommand>
    {
        private readonly IRouteContentRepository _routeContentRepository;
        private readonly IRouteCategoryRepository _routeCategoryRepository;

        public CreateRouteContentCommandValidator(IRouteContentRepository routeContentRepository, IRouteCategoryRepository routeCategoryRepository)
        {
            _routeContentRepository = routeContentRepository;
            _routeCategoryRepository = routeCategoryRepository;

            RuleFor(x => x.RouteContents)
                .NotEmpty().WithMessage("At least one route content is required");

            RuleForEach(x => x.RouteContents).ChildRules(content =>
            {
                content.RuleFor(x => x.Title)
                    .NotEmpty().WithMessage("Title is required")
                    .MaximumLength(255).WithMessage("Title must not exceed 255 characters");

                content.RuleFor(x => x.Description)
                    .MaximumLength(500).WithMessage("Description must not exceed 500 characters");

                content.RuleFor(x => x.DirectUrl)
                    .MaximumLength(500).WithMessage("Direct URL must not exceed 500 characters");

                content.RuleFor(x => x.MetaTitle)
                    .MaximumLength(255).WithMessage("Meta title must not exceed 255 characters");

                content.RuleFor(x => x.MetaDescription)
                    .MaximumLength(500).WithMessage("Meta description must not exceed 500 characters");

                content.RuleFor(x => x.Keywords)
                    .MaximumLength(500).WithMessage("Keywords must not exceed 500 characters");

                content.RuleFor(x => x.CategoryId)
                    .NotEmpty().WithMessage("Category ID is required")
                    .MustAsync(async (categoryId, cancellation) =>
                    {
                        var category = await _routeCategoryRepository.GetByIdAsync(categoryId);
                        return category != null && !category.Deleted;
                    }).WithMessage("Category does not exist");

                content.RuleFor(x => x.LanguageId)
                    .NotEmpty().WithMessage("Language ID is required");

                content.RuleFor(x => x.GroupId)
                    .NotEmpty().WithMessage("Group ID is required");
            });
        }
    }
}
