using AutoMapper;
using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.Features.RouteContent.Commands.CreateRouteContent
{
    public class CreateRouteContentCommandHandler : IRequestHandler<CreateRouteContentCommand, ActionResponse<bool>>
    {
        private readonly IRouteContentRepository _routeContentRepository;
        private readonly IRouteCategoryRepository _routeCategoryRepository;
        private readonly IMapper _mapper;
        private readonly IAppLogger<CreateRouteContentCommandHandler> _logger;
        private readonly IUnitOfWork _uow;
        private readonly IUserPrincipal _userPrincipal;

        public CreateRouteContentCommandHandler(IRouteContentRepository routeContentRepository,
                                              IRouteCategoryRepository routeCategoryRepository,
                                              IMapper mapper,
                                              IAppLogger<CreateRouteContentCommandHandler> logger,
                                              IUnitOfWork uow,
                                              IUserPrincipal userPrincipal)
        {
            _routeContentRepository = routeContentRepository;
            _routeCategoryRepository = routeCategoryRepository;
            _mapper = mapper;
            _logger = logger;
            _uow = uow;
            _userPrincipal = userPrincipal;
        }

        public async Task<ActionResponse<bool>> Handle(CreateRouteContentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var validator = new CreateRouteContentCommandValidator(_routeContentRepository, _routeCategoryRepository);
                var validatorResult = await validator.ValidateAsync(request, cancellationToken);
                
                if (validatorResult.IsValid)
                {
                    var routeContents = new List<Domain.Entities.RouteContent>();
                    
                    foreach (var routeContentRequest in request.RouteContents)
                    {
                        var routeContent = _mapper.Map<Domain.Entities.RouteContent>(routeContentRequest);
                        routeContent.Id = Guid.NewGuid();
                        routeContent.UserId = _userPrincipal.UserId;
                        routeContent.ProjectId = _userPrincipal.ProjectId.ToGuid();
                        routeContent.LanguageId = routeContentRequest.LanguageId;
                        routeContent.GroupId = routeContentRequest.GroupId ?? Guid.NewGuid();
                        routeContent.DateCreated = DateTime.Now;
                        
                        routeContents.Add(routeContent);
                    }

                    await _routeContentRepository.CreateRangeAsync(routeContents);
                    await _uow.SaveChangesAsync(cancellationToken);
                    
                    _logger.LogInformation("Route contents created successfully", request.RouteContents.Count);
                    return ActionResponse<bool>.Success(true, StatusCode.Ok);
                }
                
                return ActionResponse<bool>.Fail(validatorResult.Errors.First().ErrorMessage, StatusCode.BadRequest);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Route contents not created", request, ex);
                return ActionResponse<bool>.Fail($"An error occurred. Error Message: {ex.Message}", StatusCode.BadRequest);
            }
        }
    }
}
