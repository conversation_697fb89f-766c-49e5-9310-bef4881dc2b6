using MediatR;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.RouteContent;

namespace TmobCore.Cms.Application.Features.RouteContent.Queries.GetRouteContents
{
    public class GetRouteContentsQuery : IRequest<ActionResponse<List<RouteContentResponse>>>
    {
        public string? SearchTerm { get; set; }
        public Guid? GroupId { get; set; }
        public Guid? LanguageId { get; set; }
        public Guid? CategoryId { get; set; }
        public BaseStatus? Status { get; set; }
    }
}
