using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.RouteContent;

namespace TmobCore.Cms.Application.Features.RouteContent.Queries.GetRouteContents
{
    public class GetRouteContentsQueryHandler : IRequestHandler<GetRouteContentsQuery, ActionResponse<List<RouteContentResponse>>>
    {
        private readonly IRouteContentRepository _routeContentRepository;
        private readonly IMapper _mapper;
        private readonly IAppLogger<GetRouteContentsQueryHandler> _logger;
        private readonly IUserPrincipal _userPrincipal;

        public GetRouteContentsQueryHandler(IRouteContentRepository routeContentRepository,
                                          IMapper mapper,
                                          IAppLogger<GetRouteContentsQueryHandler> logger,
                                          IUserPrincipal userPrincipal)
        {
            _routeContentRepository = routeContentRepository;
            _mapper = mapper;
            _logger = logger;
            _userPrincipal = userPrincipal;
        }

        public async Task<ActionResponse<List<RouteContentResponse>>> Handle(GetRouteContentsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var projectId = _userPrincipal.ProjectId.ToGuid();
                
                var query = _routeContentRepository.GetQuery(
                    x => !x.Deleted && (projectId == null || x.ProjectId == projectId),
                    x => x.Category);

                // Apply filters
                if (!string.IsNullOrEmpty(request.SearchTerm))
                {
                    query = query.Where(x => x.Title.Contains(request.SearchTerm) || 
                                           x.Description.Contains(request.SearchTerm) ||
                                           x.Content.Contains(request.SearchTerm));
                }

                if (request.GroupId.HasValue)
                {
                    query = query.Where(x => x.GroupId == request.GroupId);
                }

                if (request.LanguageId.HasValue)
                {
                    query = query.Where(x => x.LanguageId == request.LanguageId);
                }

                if (request.CategoryId.HasValue)
                {
                    query = query.Where(x => x.CategoryId == request.CategoryId);
                }

                if (request.Status.HasValue)
                {
                    query = query.Where(x => x.Status == (Domain.Common.BaseStatus)request.Status);
                }

                var routeContents = await query
                    .OrderBy(x => x.DateCreated)
                    .ToListAsync(cancellationToken);

                var response = _mapper.Map<List<RouteContentResponse>>(routeContents);
                
                _logger.LogInformation("Route contents retrieved successfully", routeContents.Count);
                return ActionResponse<List<RouteContentResponse>>.Success(response, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Error retrieving route contents", request, ex);
                return ActionResponse<List<RouteContentResponse>>.Fail($"An error occurred. Error Message: {ex.Message}", StatusCode.BadRequest);
            }
        }
    }
}
