using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Models.RouteCategory
{
    public class RouteCategoryRequest : BaseRequest
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
    }

    public class CreateRouteCategoryRequest : BaseRequest
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
    }

    public class UpdateRouteCategoryRequest : BaseRequest
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
    }
}
