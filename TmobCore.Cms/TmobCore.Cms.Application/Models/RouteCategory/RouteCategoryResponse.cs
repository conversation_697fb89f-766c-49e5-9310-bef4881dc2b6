using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Models.RouteCategory
{
    public class RouteCategoryResponse : BaseResponse
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public Guid ProjectId { get; set; }
        public Guid GroupId { get; set; }
        public Guid? LanguageId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public DateTime? DateCreated { get; set; }
        public DateTime? DateModified { get; set; }
    }
}
