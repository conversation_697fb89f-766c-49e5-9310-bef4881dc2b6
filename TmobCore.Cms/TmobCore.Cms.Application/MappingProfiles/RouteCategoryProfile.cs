using AutoMapper;
using TmobCore.Cms.Application.Models.RouteCategory;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.MappingProfiles
{
    public class RouteCategoryProfile : Profile
    {
        public RouteCategoryProfile()
        {
            CreateMap<CreateRouteCategoryRequest, RouteCategory>()
                .ReverseMap();

            CreateMap<UpdateRouteCategoryRequest, RouteCategory>()
                .ReverseMap();

            CreateMap<RouteCategory, RouteCategoryResponse>()
                .ReverseMap();

            CreateMap<RouteCategoryRequest, RouteCategory>()
                .ReverseMap();
        }
    }
}
