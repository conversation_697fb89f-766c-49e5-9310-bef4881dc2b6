using AutoMapper;
using TmobCore.Cms.Application.Models.RouteContent;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.MappingProfiles
{
    public class RouteContentProfile : Profile
    {
        public RouteContentProfile()
        {
            CreateMap<CreateRouteContentRequest, RouteContent>()
                .ReverseMap();

            CreateMap<UpdateRouteContentRequest, RouteContent>()
                .ReverseMap();

            CreateMap<RouteContent, RouteContentResponse>()
                .ReverseMap();

            CreateMap<RouteContentRequest, RouteContent>()
                .ReverseMap();
        }
    }
}
