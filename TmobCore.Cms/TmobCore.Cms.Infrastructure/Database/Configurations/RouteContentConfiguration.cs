using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TmobCore.Cms.Domain.Common;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Infrastructure.Database.Configurations
{
    public class RouteContentConfiguration : IEntityTypeConfiguration<RouteContent>
    {
        public void Configure(EntityTypeBuilder<RouteContent> builder)
        {
            builder.HasKey(rc => rc.Id);
            builder.Property(rc => rc.Title).IsRequired().HasMaxLength(255);
            builder.Property(rc => rc.Description).HasMaxLength(500);
            builder.Property(rc => rc.DirectUrl).HasMaxLength(500);
            builder.Property(rc => rc.MetaTitle).HasMaxLength(255);
            builder.Property(rc => rc.MetaDescription).HasMaxLength(500);
            builder.Property(rc => rc.Keywords).HasMaxLength(500);
            builder.Property(rc => rc.Content).HasColumnType("nvarchar(max)");
            builder.Property(rc => rc.Status).HasDefaultValue(BaseStatus.Draft);
            builder.Property(rc => rc.Deleted).HasDefaultValue(false);

            builder.HasOne(rc => rc.Category)
                .WithMany(category => category.RouteContents)
                .HasForeignKey(rc => rc.CategoryId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
