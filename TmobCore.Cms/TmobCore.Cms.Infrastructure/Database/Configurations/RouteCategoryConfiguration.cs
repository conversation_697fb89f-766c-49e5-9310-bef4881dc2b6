using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TmobCore.Cms.Domain.Common;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Infrastructure.Database.Configurations
{
    public class RouteCategoryConfiguration : IEntityTypeConfiguration<RouteCategory>
    {
        public void Configure(EntityTypeBuilder<RouteCategory> builder)
        {
            builder.HasKey(rc => rc.Id);
            builder.Property(rc => rc.Title).IsRequired().HasMaxLength(255);
            builder.Property(rc => rc.Description).HasMaxLength(500);
            builder.Property(rc => rc.Icon).HasMaxLength(255);
            builder.Property(rc => rc.Status).HasDefaultValue(BaseStatus.Draft);
            builder.Property(rc => rc.Deleted).HasDefaultValue(false);

            builder.HasMany(rc => rc.RouteContents)
                .WithOne(content => content.Category)
                .HasForeignKey(content => content.CategoryId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
