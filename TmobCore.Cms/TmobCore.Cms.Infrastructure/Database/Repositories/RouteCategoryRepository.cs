using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Domain.Entities;
using TmobCore.Cms.Infrastructure.Database.DatabaseContext;

namespace TmobCore.Cms.Infrastructure.Database.Repositories
{
    public class RouteCategoryRepository : GenericRepository<RouteCategory>, IRouteCategoryRepository
    {
        public RouteCategoryRepository(CmsCoreDatabaseContext context) : base(context)
        {
        }

        public async Task<bool> IsTitleUnique(string title, Guid? id = null)
        {
            var query = _context.RouteCategories.AsQueryable();
            if (id.HasValue)
                query = query.Where(x => x.Id != id);

            return !await query.AnyAsync(x => x.Title == title && !x.Deleted);
        }
    }
}
