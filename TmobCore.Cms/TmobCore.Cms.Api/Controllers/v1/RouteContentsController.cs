using MediatR;
using Microsoft.AspNetCore.Mvc;
using TmobCore.Cms.Application.Features.RouteContent.Commands.CreateRouteContent;
using TmobCore.Cms.Application.Features.RouteContent.Commands.DeleteRouteContent;
using TmobCore.Cms.Application.Features.RouteContent.Queries.GetRouteContents;

namespace TmobCore.Cms.Api.Controllers.v1
{
    /// <summary>
    /// Route Contents management endpoints for homepage Popular Routes section
    /// </summary>
    [Route("api/v1/[controller]")]
    [ApiController]
    public class RouteContentsController : ControllerBase
    {
        private readonly IMediator _mediator;

        public RouteContentsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// Get all route contents with optional filtering
        /// </summary>
        /// <param name="searchTerm">Search term for title, description, and content</param>
        /// <param name="groupId">Filter by group ID for multi-language support</param>
        /// <param name="languageId">Filter by language ID</param>
        /// <param name="categoryId">Filter by category ID</param>
        /// <param name="status">Filter by status (0: Draft, 1: Published, 2: Unpublished)</param>
        /// <returns>List of route contents</returns>
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Get([FromQuery] string? searchTerm, [FromQuery] Guid? groupId, [FromQuery] Guid? languageId, [FromQuery] Guid? categoryId, [FromQuery] int? status)
        {
            var query = new GetRouteContentsQuery 
            { 
                SearchTerm = searchTerm, 
                GroupId = groupId, 
                LanguageId = languageId,
                CategoryId = categoryId,
                Status = status.HasValue ? (Application.Models.Common.BaseStatus)status.Value : null
            };
            
            var response = await _mediator.Send(query);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        /// <summary>
        /// Create new route contents
        /// </summary>
        /// <param name="command">Route content creation data</param>
        /// <returns>Success status</returns>
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Post([FromBody] CreateRouteContentCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        /// <summary>
        /// Delete route contents by IDs
        /// </summary>
        /// <param name="command">Route content IDs to delete</param>
        /// <returns>Number of deleted contents</returns>
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Delete([FromBody] DeleteRouteContentCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }
    }
}
