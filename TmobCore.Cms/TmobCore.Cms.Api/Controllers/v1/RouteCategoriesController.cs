using MediatR;
using Microsoft.AspNetCore.Mvc;
using TmobCore.Cms.Application.Features.RouteCategory.Commands.CreateRouteCategory;
using TmobCore.Cms.Application.Features.RouteCategory.Commands.DeleteRouteCategory;
using TmobCore.Cms.Application.Features.RouteCategory.Commands.UpdateRouteCategory;
using TmobCore.Cms.Application.Features.RouteCategory.Commands.UpdateRouteCategoryStatus;
using TmobCore.Cms.Application.Features.RouteCategory.Queries.GetRouteCategories;
using TmobCore.Cms.Application.Features.RouteCategory.Queries.GetRouteCategoryById;

namespace TmobCore.Cms.Api.Controllers.v1
{
    /// <summary>
    /// Route Categories management endpoints for homepage Popular Routes section
    /// </summary>
    [Route("api/v1/[controller]")]
    [ApiController]
    public class RouteCategoriesController : ControllerBase
    {
        private readonly IMediator _mediator;

        public RouteCategoriesController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// Get all route categories with optional filtering
        /// </summary>
        /// <param name="searchTerm">Search term for title and description</param>
        /// <param name="groupId">Filter by group ID for multi-language support</param>
        /// <param name="languageId">Filter by language ID</param>
        /// <param name="status">Filter by status (0: Draft, 1: Published, 2: Unpublished)</param>
        /// <returns>List of route categories</returns>
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Get([FromQuery] string? searchTerm, [FromQuery] Guid? groupId, [FromQuery] Guid? languageId, [FromQuery] int? status)
        {
            var query = new GetRouteCategoriesQuery 
            { 
                SearchTerm = searchTerm, 
                GroupId = groupId, 
                LanguageId = languageId,
                Status = status.HasValue ? (Application.Models.Common.BaseStatus)status.Value : null
            };
            
            var response = await _mediator.Send(query);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        /// <summary>
        /// Get route category by ID
        /// </summary>
        /// <param name="id">Route category ID</param>
        /// <returns>Route category details</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Get(Guid id)
        {
            var response = await _mediator.Send(new GetRouteCategoryByIdQuery { Id = id });
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        /// <summary>
        /// Create new route categories
        /// </summary>
        /// <param name="command">Route category creation data</param>
        /// <returns>Success status</returns>
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Post([FromBody] CreateRouteCategoryCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        /// <summary>
        /// Update existing route categories
        /// </summary>
        /// <param name="command">Route category update data</param>
        /// <returns>Success status</returns>
        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Put([FromBody] UpdateRouteCategoryCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        /// <summary>
        /// Delete route categories by IDs
        /// </summary>
        /// <param name="command">Route category IDs to delete</param>
        /// <returns>Number of deleted categories</returns>
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Delete([FromBody] DeleteRouteCategoryCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        /// <summary>
        /// Update status of multiple route categories
        /// </summary>
        /// <param name="command">Route category IDs and new status</param>
        /// <returns>Success status</returns>
        [HttpPut("status")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> UpdateStatus([FromBody] UpdateRouteCategoryStatusCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }
    }
}
