using TmobCore.Cms.Domain.Common;

namespace TmobCore.Cms.Domain.Entities
{
    public class RouteContent : BaseEntityExtended
    {
        public Guid UserId { get; set; }
        public Guid ProjectId { get; set; }
        public Guid GroupId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DirectUrl { get; set; } = string.Empty;
        public string MetaTitle { get; set; } = string.Empty;
        public string MetaDescription { get; set; } = string.Empty;
        public string Keywords { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        
        public Guid CategoryId { get; set; }
        public RouteCategory Category { get; set; }
    }
}
